export namespace mesProductionReport {
  export interface ReportQuery {
    Deck?: string;
    StartDate?: string; // 假设日期格式为 ISO 字符串，例如 "2023-03-15T00:00:00Z"
    EndDate?: string; // 同上
    Type?: number; // 1: 产能; 2: 质量; 3: 能耗
  }

  /** 计划标准配置 */
  export interface PlanStandardConfig {
    /** ID */
    id?: number;
    /** 工厂 */
    factory?: string;
    /** 车间 */
    workShop?: string;
    /** 产线 */
    workLine?: string;
    /** 机台编号 */
    deckNo?: string;
    /** 机台 */
    deck?: string;
    /** 每天预计生产数量 */
    dailyExpectedQuantity?: number;
    /** 每天运行时长（小时） */
    dailyRunningHours?: number;
    /** 日期 */
    date?: string;
    /** PPM值 */
    ppm?: number;
  }
}
