<template>
  <div class="main-box">
    <ProChart
      ref="proChartRef"
      :options="chartOptions"
      :fetch-api="fetchData"
      :tool-buttons="['refresh', 'download', 'table', 'compare']"
      :search-fields="searchFields"
      @data-loaded="handleDataLoaded"
      :compare-list="compareList"
      default-compare-id="达成率"
      :machines="machines"
      :init-params="initParams"
      id="651535198396485"
    >
      <template #toolbar-left>
        <el-button type="primary" @click="openPlanStandardDialog"> 修改计划标准 </el-button>
      </template>
      <!-- 修改计划标准弹窗 -->
    </ProChart>
    <el-dialog v-model="planStandardDialogVisible" title="修改计划标准" width="500px" :before-close="handleDialogClose">
      <el-form ref="planStandardFormRef" :model="planStandardForm" :rules="planStandardRules" label-width="120px">
        <el-form-item label="PPM" prop="ppm">
          <el-input-number
            v-model="planStandardForm.ppm"
            :min="0"
            :precision="0"
            placeholder="请输入PPM值"
            style="width: 100%"
            @input="handlePpmChange"
          />
        </el-form-item>
        <el-form-item label="每天运行时长" prop="dailyRunningHours">
          <el-input-number
            v-model="planStandardForm.dailyRunningHours"
            :min="0"
            :max="24"
            :precision="1"
            placeholder="请输入每天运行时长"
            style="width: 100%"
          />
          <span style="margin-left: 8px; color: #909399">小时</span>
        </el-form-item>
        <el-form-item label="每天预计生产数量" prop="dailyExpectedQuantity">
          <el-input-number
            v-model="planStandardForm.dailyExpectedQuantity"
            :min="0"
            :precision="0"
            placeholder="请输入每天预计生产数量"
            style="width: 100%"
            @input="handleDailyExpectedQuantityChange"
          />
          <span style="margin-left: 8px; color: #909399">件</span>
        </el-form-item>
        <el-form-item>
          <div style="font-size: 12px; color: #909399">
            <div style="margin-bottom: 8px; color: #f56c6c">注意：PPM模式和每天预计生产数量模式二选一，输入其中一个会自动清空另一个</div>
            <template v-if="planStandardForm.dailyExpectedQuantity">
              <strong>当前模式：每天预计生产数量</strong>
              <br />
              每小时标准 = 每天预计生产数量 ÷
              {{ planStandardForm.dailyRunningHours ? planStandardForm.dailyRunningHours : "24小时（按整天计算）" }}
              <br />
              天模式显示：每天预计生产数量，月模式显示：每天预计生产数量 × 30天
            </template>
            <template v-else-if="planStandardForm.ppm">
              <strong>当前模式：PPM + 运行时长</strong>
              <br />
              每小时标准 = PPM × 60，运行时长{{ planStandardForm.dailyRunningHours }}小时
              <br />
              天模式显示：每小时标准 × 运行时长，月模式显示：每小时标准 × 运行时长 × 30天
            </template>
            <template v-else>
              <strong>请选择一种模式：</strong>
              <br />
              1. PPM模式：输入PPM值和每天运行时长
              <br />
              2. 每天预计生产数量模式：直接输入每天的生产目标
            </template>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="planStandardDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePlanStandard" :loading="saving"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx" name="计划达成率">
import { ref, onMounted } from "vue";
import type { ColumnProps } from "@/components/ProTable/interface";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import moment from "moment";
import { productionReportCapacityApi } from "@/api/modules/mes/productionReport";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

// 计划标准弹窗相关
const planStandardDialogVisible = ref(false);
const planStandardFormRef = ref<FormInstance>();
const proChartRef = ref<any>(null);
const saving = ref(false);
const planStandardForm = ref({
  ppm: 0,
  dailyRunningHours: 8,
  dailyExpectedQuantity: undefined as number | undefined
});

// 用户自定义的计划标准值（每小时）
const customHourlyStandard = ref<number | null>(null);
// 当前时间模式
const currentTimeMode = ref<string>("Hour");
// 当前是否为每天预计生产数量模式
const isDailyQuantityMode = ref<boolean>(false);

const planStandardRules: FormRules = {
  ppm: [
    {
      validator: (rule, value, callback) => {
        // 如果没有输入每天预计生产数量，则PPM必须输入
        if (!planStandardForm.value.dailyExpectedQuantity && (!value || value <= 0)) {
          callback(new Error("请输入PPM值或每天预计生产数量"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  dailyRunningHours: [
    {
      validator: (rule, value, callback) => {
        // 如果使用PPM模式，运行时长必须输入
        if (planStandardForm.value.ppm > 0 && (!value || value <= 0 || value > 24)) {
          callback(new Error("使用PPM模式时，运行时长应在0-24小时之间"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  dailyExpectedQuantity: [
    {
      validator: (rule, value, callback) => {
        // 如果没有输入PPM，则每天预计生产数量必须输入
        if (!planStandardForm.value.ppm && (!value || value <= 0)) {
          callback(new Error("请输入每天预计生产数量或PPM值"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
};

// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 3
});
const machines = ref<any[]>([]);
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 颜色配置常量
const COLORS = {
  实际产出: "#00bfff",
  计划标准: "#ff4d4f",
  达成率: "#fac858",
  font: chartColor,
  splitLine: "#eee"
};

const compareList = [
  {
    label: t("common.compareList.实际产出"),
    value: "实际产出"
  },
  {
    label: t("common.compareList.计划标准"),
    value: "计划标准"
  },
  {
    label: t("common.compareList.达成率"),
    value: "达成率"
  }
];

// 搜索字段配置
const searchFields = ref<ColumnProps[]>([
  {
    prop: "time",
    label: t("common.compareList.time"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
      }
    },
    isShow: false
  }
]);

// 图表配置
const chartOptions = ref({
  title: {
    // text: "计划达成率统计看板",
    subtext: t("common.compareList.averageAchievementRate") + ": 0", //"平均达成率：0",
    left: "center",
    top: -12,
    textStyle: {
      fontSize: 16,
      color: COLORS.font
    },
    subtextStyle: {
      color: COLORS.font
    }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("productionReport.capacity.switchToLine"), // 切换为折线图
          bar: t("productionReport.capacity.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const 实际产出 = params.find((p: any) => p.seriesName === t("common.compareList.实际产出"));
      const 计划标准 = params.find((p: any) => p.seriesName === t("common.compareList.计划标准"));
      const 达成率 = params.find((p: any) => p.seriesName === t("common.compareList.达成率"));
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.实际产出};border-radius:50%"></span>
            ${t("common.compareList.实际产出")}: ${实际产出?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.计划标准};border-radius:50%"></span>
            ${t("common.compareList.计划标准")}: ${计划标准?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.达成率};border-radius:50%"></span>
            ${t("common.compareList.达成率")}: ${达成率?.data}%
          </div>
        </div>
      `;
    }
  },
  legend: {
    data: [t("common.compareList.实际产出"), t("common.compareList.计划标准"), t("common.compareList.达成率")],
    top: 17,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: [],
    name: t("common.compareList.time"),
    axisLabel: {
      color: COLORS.font,
      interval: 0, // 强制显示所有标签
      rotate: 45 // 旋转标签以避免重叠
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.quantityOrRatio"), //数量/比率 (%)
      min: 0,
      // max: 100,
      axisLabel: {
        color: COLORS.font,
        formatter: (value: number) => `${value}`
      }
    }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    // top: 100,
    containLabel: true
  },
  series: []
});
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   searchParam.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    console.error("机台列表请求失败:", error);
    ElMessage.error("获取机台列表失败");
  }
};
function transformData(responseData, timeType) {
  const machineMap = new Map();
  const timeSet = new Set();
  const machineIdSet = new Set();

  // 根据时间类型确定时间单位和格式
  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "YYYY";
  }

  // 第一次遍历：聚合数据
  responseData.forEach(item => {
    const machine = item.machine;
    const timeKey = moment(item.start_time).format(formatPattern);

    if (!machineMap.has(machine)) {
      machineMap.set(machine, {
        machine,
        timeBuckets: new Map(), // 存储时间段聚合数据
        total实际产出: 0,
        total计划标准: 0,
        dataCount: 0
      });
    }

    const machineData = machineMap.get(machine);
    const bucket = machineData.timeBuckets.get(timeKey) || {
      实际产出: 0,
      计划标准: 0,
      count: 0
    };

    // 累加数据到时间段
    bucket.实际产出 += item.actualquantity || 0;
    bucket.计划标准 += item.plannedquantity || 0;
    bucket.count += 1;
    machineData.timeBuckets.set(timeKey, bucket);

    // 更新总统计
    machineData.total实际产出 += item.actualquantity || 0;
    machineData.total计划标准 += item.plannedquantity || 0;
    machineData.dataCount += 1;

    // 记录存在的时间点和机台
    timeSet.add(timeKey);
    machineIdSet.add(machine);
  });

  // 第二次遍历：生成排序后的时间序列
  const categories = Array.from(timeSet).sort((a, b) => moment(a as string, formatPattern).valueOf() - moment(b as string, formatPattern).valueOf());

  machineMap.forEach(machineData => {
    // 确保所有时间点都有数据（没有的补0）
    const sortedData = categories.map(timeKey => {
      const bucket = machineData.timeBuckets.get(timeKey) || {
        实际产出: 0,
        计划标准: 0,
        count: 0
      };

      // 计算达成率：实际产出 / 计划标准 * 100
      const 达成率 = bucket.计划标准 > 0 ? ((bucket.实际产出 / bucket.计划标准) * 100).toFixed(1) : 0;

      return {
        实际产出: bucket.实际产出,
        计划标准: bucket.计划标准,
        达成率: parseFloat(达成率.toString())
      };
    });

    // 转换为需要的数组格式
    machineData.实际产出 = sortedData.map(d => d.实际产出);
    machineData.计划标准 = sortedData.map(d => d.计划标准);
    machineData.达成率 = sortedData.map(d => d.达成率);

    // 计算总达成率：总实际产出 / 总计划标准 * 100
    machineData.total实际产出 = machineData.total实际产出.toFixed(1);
    machineData.total计划标准 = machineData.total计划标准.toFixed(1);
    machineData.total达成率 = machineData.total计划标准 > 0 ? ((machineData.total实际产出 / machineData.total计划标准) * 100).toFixed(1) : "0";
  });

  // 构建最终数据结构
  const allmachine = Array.from(machineMap.values());
  const compare = [
    {
      实际产出: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.实际产出,
        total: parseFloat(machine.total实际产出)
      })),
      计划标准: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.计划标准,
        total: parseFloat(machine.total计划标准)
      })),
      达成率: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.达成率,
        total: parseFloat(machine.total达成率)
      }))
    }
  ];

  return {
    allmachine,
    compare,
    categories,
    machines: Array.from(machineIdSet).map(id => ({ id, name: id }))
  };
}

// 修改数据获取函数
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
    EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
  };
  const query = {
    ...time,
    type: params.type
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);
  let mode = "Hour"; // 默认值
  if (data && (data as any).list && (data as any).list.length > 0) {
    mode = (data as any).list[0].type;
  }
  // 保存当前时间模式
  currentTimeMode.value = mode;
  const data1 = transformData((data as any).list, mode);
  machines.value = data1.machines;

  // 普通模式数据请求
  if (!params.compareMode) {
    const machine = params.machine;
    const machineInfo = data1.allmachine.find(item => item.machine === machine) || data1.allmachine[0];
    if (!machineInfo) {
      console.error(`未找到机台 ${machine} 的数据`);
      return {
        data: {
          categories: [],
          seriesData: [[], [], []],
          isCompare: false
        }
      };
    }
    const { 实际产出, 计划标准, 达成率 } = machineInfo;
    return {
      data: {
        categories: data1.categories,
        seriesData: [实际产出, 计划标准, 达成率],
        isCompare: false
      }
    };
  }

  // 对比模式数据请求
  return {
    data: {
      isCompare: true,
      categories: data1.categories,
      compare: data1.compare
    }
  };
};

// 修改数据加载回调
const handleDataLoaded = (data: any) => {
  // 普通模式数据处理
  if (!data.isCompare) {
    const [实际产出, 原始计划标准, 达成率] = data.seriesData;

    // 如果用户设置了自定义计划标准，根据当前时间模式计算显示值；否则使用原始数据
    let 计划标准: number[];
    let 重新计算达成率 = false;
    if (customHourlyStandard.value !== null) {
      const displayStandard = calculateStandardByMode(customHourlyStandard.value, currentTimeMode.value, isDailyQuantityMode.value);
      计划标准 = new Array(data.categories.length).fill(displayStandard);
      重新计算达成率 = true;
    } else {
      计划标准 = 原始计划标准;
    }

    // 如果使用了自定义计划标准，需要重新计算达成率
    let 最终达成率 = 达成率;
    if (重新计算达成率) {
      最终达成率 = 实际产出.map((output: number, index: number) => {
        const standard = 计划标准[index];
        return standard > 0 ? parseFloat(((output / standard) * 100).toFixed(1)) : 0;
      });
    }

    chartOptions.value = {
      ...chartOptions.value,
      title: {
        ...chartOptions.value.title,
        subtext: `${t("common.compareList.averageAchievementRate")}：${(最终达成率.reduce((a: number, b: number) => a + b, 0) / 最终达成率.length).toFixed(1)}%`
      },
      xAxis: {
        ...chartOptions.value.xAxis,
        data: data.categories
      },
      yAxis: chartOptions.value.yAxis, // 保留原有的y轴配置
      series: [
        {
          name: t("common.compareList.实际产出"),
          type: "bar",
          data: 实际产出,
          itemStyle: { color: COLORS.实际产出 },
          label: { show: true, position: "top" }
        },
        {
          name: t("common.compareList.计划标准"),
          type: "line",
          data: 计划标准,
          itemStyle: { color: COLORS.计划标准 },
          lineStyle: { color: COLORS.计划标准, width: 2 },
          label: { show: true, position: "top" },
          symbol: "circle",
          symbolSize: 6
        },
        {
          name: t("common.compareList.达成率"),
          type: "bar",
          data: 最终达成率,
          itemStyle: { color: COLORS.达成率 },
          label: { show: true, position: "top" }
        }
      ] as any
    };
  }
};

// 根据时间模式计算计划标准值
const calculateStandardByMode = (baseHourlyStandard: number, mode: string, isDailyQuantityMode: boolean = false): number => {
  switch (mode) {
    case "Hour":
      return baseHourlyStandard; // 小时模式，直接返回每小时标准
    case "Mon":
      // 天模式
      if (isDailyQuantityMode) {
        // 每天预计生产数量模式：按24小时计算
        return baseHourlyStandard * 24;
      } else {
        // PPM + 运行时长模式：按实际运行时长计算
        const dailyHours = planStandardForm.value.dailyRunningHours || 8;
        return baseHourlyStandard * dailyHours;
      }
    case "Year":
      // 月模式
      if (isDailyQuantityMode) {
        // 每天预计生产数量模式：按24小时 × 30天计算
        return baseHourlyStandard * 24 * 30;
      } else {
        // PPM + 运行时长模式：按实际运行时长 × 30天计算
        const monthlyHours = (planStandardForm.value.dailyRunningHours || 8) * 30;
        return baseHourlyStandard * monthlyHours;
      }
    default:
      return baseHourlyStandard;
  }
};

// 处理PPM变化
const handlePpmChange = (value: number | null | undefined) => {
  if (value && value > 0) {
    // 如果输入了PPM，清空每天预计生产数量
    planStandardForm.value.dailyExpectedQuantity = undefined;
  }
};

// 处理每天预计生产数量变化
const handleDailyExpectedQuantityChange = (value: number | null | undefined) => {
  if (value && value > 0) {
    // 如果输入了每天预计生产数量，清空PPM
    planStandardForm.value.ppm = 0;
  }
};

// 计划标准弹窗相关方法
const openPlanStandardDialog = () => {
  planStandardDialogVisible.value = true;
  // 可以在这里加载当前的计划标准配置
  loadCurrentPlanStandard();
};

const handleDialogClose = () => {
  planStandardFormRef.value?.resetFields();
  planStandardDialogVisible.value = false;
};

const loadCurrentPlanStandard = async () => {
  try {
    // 获取当前机台信息作为查询参数
    const machineInfo = getCurrentMachineInfo();

    // 构建查询参数
    const queryParams: any = {
      factory: machineInfo.factory,
      workShop: machineInfo.workShop,
      workLine: machineInfo.workLine,
      deckNo: machineInfo.deckNo,
      deck: machineInfo.deck
    };

    // 调用新的API获取当前的基础标准配置
    const { data } = await productionReportCapacityApi.getBasalStandard(queryParams);
    if (data) {
      const standardData = data as any;
      planStandardForm.value = {
        ppm: standardData.ppm || 0,
        dailyRunningHours: standardData.dailyRunningHours || 8,
        dailyExpectedQuantity: standardData.dailyExpectedQuantity || undefined
      };

      // 设置编辑模式和ID
      if (standardData.id) {
        currentStandardId.value = standardData.id;
        isEditMode.value = true;
      } else {
        currentStandardId.value = null;
        isEditMode.value = false;
      }

      // 如果有数据，计算并设置自定义标准值
      if (standardData.dailyExpectedQuantity && standardData.dailyExpectedQuantity > 0) {
        // 每天预计生产数量模式：按24小时计算每小时标准
        isDailyQuantityMode.value = true;
        customHourlyStandard.value = standardData.dailyExpectedQuantity / 24;
      } else if (standardData.ppm && standardData.ppm > 0) {
        // PPM + 运行时长模式
        isDailyQuantityMode.value = false;
        customHourlyStandard.value = standardData.ppm * 60;
      }
    } else {
      // 如果没有数据，设置为新增模式
      isEditMode.value = false;
      currentStandardId.value = null;
    }
  } catch (error) {
    console.error("加载计划标准配置失败:", error);
    // 如果加载失败，设置为新增模式
    isEditMode.value = false;
    currentStandardId.value = null;
  }
};

// 更新图表中的计划标准数据并重新计算达成率
const updateChartWithNewStandard = (baseHourlyStandard: number) => {
  // 获取当前图表的series数据
  const currentSeries = chartOptions.value.series;
  if (currentSeries && currentSeries.length > 0) {
    // 找到各个series的索引
    const planStandardSeriesIndex = currentSeries.findIndex((series: any) => series.name === t("common.compareList.计划标准"));
    const actualOutputSeriesIndex = currentSeries.findIndex((series: any) => series.name === t("common.compareList.实际产出"));
    const achievementRateSeriesIndex = currentSeries.findIndex((series: any) => series.name === t("common.compareList.达成率"));

    if (planStandardSeriesIndex !== -1) {
      // 获取当前的时间点数量
      const categoriesLength = chartOptions.value.xAxis?.data?.length || 0;

      // 根据当前时间模式计算实际显示的计划标准值
      const displayStandard = calculateStandardByMode(baseHourlyStandard, currentTimeMode.value, isDailyQuantityMode.value);

      // 创建新的计划标准数据数组，所有时间点都使用相同的displayStandard值
      const newPlanStandardData = new Array(categoriesLength).fill(displayStandard);

      // 更新计划标准series的数据
      (currentSeries[planStandardSeriesIndex] as any).data = newPlanStandardData;

      // 如果存在实际产出和达成率series，重新计算达成率
      if (actualOutputSeriesIndex !== -1 && achievementRateSeriesIndex !== -1) {
        const actualOutputData = (currentSeries[actualOutputSeriesIndex] as any).data;

        // 重新计算达成率：实际产出 / 计划标准 * 100
        const newAchievementRateData = actualOutputData.map((actualOutput: number) => {
          return displayStandard > 0 ? parseFloat(((actualOutput / displayStandard) * 100).toFixed(1)) : 0;
        });

        // 更新达成率series的数据
        (currentSeries[achievementRateSeriesIndex] as any).data = newAchievementRateData;

        // 重新计算平均达成率
        const avgAchievementRate =
          newAchievementRateData.length > 0
            ? (newAchievementRateData.reduce((a: number, b: number) => a + b, 0) / newAchievementRateData.length).toFixed(1)
            : 0;

        // 更新图表标题中的平均达成率
        chartOptions.value = {
          ...chartOptions.value,
          title: {
            ...chartOptions.value.title,
            subtext: `${t("common.compareList.averageAchievementRate")}：${avgAchievementRate}%`
          }
        };
      }

      // 触发图表更新
      chartOptions.value = { ...chartOptions.value };

      console.log(`计划标准已更新：基础每小时 ${baseHourlyStandard} 件，当前模式 ${currentTimeMode.value}，显示值 ${displayStandard} 件`);
    }
  } else {
    // 如果当前没有series数据，说明图表还没有加载数据
    // 我们可以设置一个标记，等数据加载后再应用新的计划标准
    console.log("图表数据尚未加载，计划标准将在数据加载后应用");
  }
};

// 添加一个标识来判断是新增还是修改
const isEditMode = ref(false);
const currentStandardId = ref<number | null>(null);

// 获取当前选中的机台信息
const getCurrentMachineInfo = () => {
  let currentMachine = null;

  if (proChartRef.value) {
    try {
      const params = proChartRef.value.getParams();
      const machineId = params.machine;
      currentMachine = machines.value.find(m => m.id === machineId);
    } catch (error) {
      console.warn("无法获取当前机台信息:", error);
    }
  }

  if (!currentMachine && machines.value.length > 0) {
    currentMachine = machines.value[0];
  }

  return {
    factory: "",
    workShop: "",
    workLine: "",
    deckNo: (currentMachine as any)?.id || "",
    deck: (currentMachine as any)?.name || ""
  };
};

const savePlanStandard = async () => {
  if (!planStandardFormRef.value) return;

  try {
    await planStandardFormRef.value.validate();
    saving.value = true;

    // 获取当前机台信息
    const machineInfo = getCurrentMachineInfo();

    const params: any = {
      ...planStandardForm.value,
      ...machineInfo,
      date: moment().format("YYYY-MM-DD HH:mm:ss")
    };

    // 如果是修改模式，添加ID
    if (isEditMode.value && currentStandardId.value) {
      params.id = currentStandardId.value;
    }

    let hourlyStandard: number;

    // 根据输入方式计算计划标准值
    if (planStandardForm.value.dailyExpectedQuantity && planStandardForm.value.dailyExpectedQuantity > 0) {
      // 每天预计生产数量模式：按24小时计算每小时标准
      isDailyQuantityMode.value = true;
      hourlyStandard = planStandardForm.value.dailyExpectedQuantity / 24;
    } else {
      // PPM + 运行时长模式：每小时 = PPM × 60
      isDailyQuantityMode.value = false;
      hourlyStandard = planStandardForm.value.ppm * 60;
    }

    // 保存用户自定义的计划标准值
    customHourlyStandard.value = hourlyStandard;

    // 先更新本地图表数据，不管API是否成功
    updateChartWithNewStandard(hourlyStandard);

    // 尝试保存到后端 - 根据是否存在ID判断是新增还是修改
    try {
      if (isEditMode.value && currentStandardId.value) {
        // 修改模式
        await productionReportCapacityApi.editBasalStandard(params);
        ElMessage.success("计划标准修改成功");
      } else {
        // 新增模式
        const response = await productionReportCapacityApi.addBasalStandard(params);
        // 保存新增后返回的ID，以便后续修改
        if (response.data && (response.data as any).id) {
          currentStandardId.value = (response.data as any).id;
          isEditMode.value = true;
        }
        ElMessage.success("计划标准新增成功");
      }
    } catch (apiError) {
      ElMessage.warning("数据已更新到图表，但保存到服务器失败");
    }

    planStandardDialogVisible.value = false;
  } catch (error) {
    ElMessage.error("保存失败，请稍后重试");
  } finally {
    saving.value = false;
  }
};

// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
  loadCurrentPlanStandard();
});
</script>
